import axios from '@/services/axios';

export const categoriaServicoService = {
  /**
   * Obter todas as categorias com filtros
   */
  async getCategorias(filters = {}) {
    const params = new URLSearchParams();
    
    if (filters.ativo !== undefined) params.append('ativo', filters.ativo);

    return axios.get(`/categorias-servicos?${params.toString()}`);
  },

  /**
   * Obter categoria específica
   */
  async getCategoria(id) {
    return axios.get(`/categorias-servicos/${id}`);
  },

  /**
   * Criar nova categoria
   */
  async criarCategoria(data) {
    return axios.post('/categorias-servicos', data);
  },

  /**
   * Atualizar categoria
   */
  async atualizarCategoria(id, data) {
    return axios.put(`/categorias-servicos/${id}`, data);
  },

  /**
   * Excluir categoria
   */
  async excluirCategoria(id) {
    return axios.delete(`/categorias-servicos/${id}`);
  },

  /**
   * Alternar status da categoria (ativar/desativar)
   */
  async toggleStatus(id) {
    return axios.post(`/categorias-servicos/${id}/toggle-status`);
  },

  /**
   * Obter categorias ativas para seleção
   */
  async getCategoriasAtivas() {
    return axios.get('/categorias-servicos/ativas');
  }
};

export default categoriaServicoService;
