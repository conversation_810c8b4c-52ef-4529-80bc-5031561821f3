<template>
  <div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card shadow-sm">
          <div class="card-header bg-gradient-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h4 class="mb-0">
                  <i class="fas fa-list-alt me-2"></i>
                  Tabela de Procedimentos
                </h4>
                <small class="opacity-75">Gerencie os procedimentos, serviços e produtos da clínica</small>
              </div>
              <div class="d-flex gap-2">
                <button 
                  class="btn btn-light btn-sm"
                  @click="abrirModalCategoria()"
                  title="Gerenciar Categorias"
                >
                  <i class="fas fa-tags me-1"></i>
                  Categorias
                </button>
                <button 
                  class="btn btn-success btn-sm"
                  @click="abrirModalProcedimento()"
                >
                  <i class="fas fa-plus me-1"></i>
                  Novo Procedimento
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filtros -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-4">
                <label class="form-label">Buscar</label>
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-search"></i>
                  </span>
                  <input
                    type="text"
                    class="form-control"
                    v-model="filtros.busca"
                    placeholder="Nome, código ou descrição..."
                    @input="buscarProcedimentos"
                  >
                </div>
              </div>

              <div class="col-md-3">
                <label class="form-label">Categoria</label>
                <select class="form-select" v-model="filtros.categoria_id" @change="buscarProcedimentos">
                  <option value="">Todas as categorias</option>
                  <option v-for="categoria in categorias" :key="categoria.id" :value="categoria.id">
                    {{ categoria.nome }}
                  </option>
                </select>
              </div>

              <div class="col-md-2">
                <label class="form-label">Tipo</label>
                <select class="form-select" v-model="filtros.tipo" @change="buscarProcedimentos">
                  <option value="">Todos os tipos</option>
                  <option value="servico">Serviço</option>
                  <option value="produto">Produto</option>
                  <option value="procedimento">Procedimento</option>
                </select>
              </div>

              <div class="col-md-2">
                <label class="form-label">Status</label>
                <select class="form-select" v-model="filtros.ativo" @change="buscarProcedimentos">
                  <option value="">Todos</option>
                  <option value="1">Ativos</option>
                  <option value="0">Inativos</option>
                </select>
              </div>

              <div class="col-md-1 d-flex align-items-end">
                <button class="btn btn-outline-secondary w-100" @click="limparFiltros">
                  <i class="fas fa-eraser"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabela -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-body p-0">
            <div v-if="loading" class="text-center p-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Carregando...</span>
              </div>
            </div>

            <div v-else-if="procedimentos.length === 0" class="text-center p-4">
              <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">Nenhum procedimento encontrado</h5>
              <p class="text-muted">
                {{ filtros.busca || filtros.categoria_id || filtros.tipo ? 
                   'Tente ajustar os filtros ou' : 'Comece' }} 
                <a href="#" @click.prevent="abrirModalProcedimento()" class="text-decoration-none">
                  criando um novo procedimento
                </a>
              </p>
            </div>

            <div v-else class="table-responsive">
              <table class="table table-hover mb-0">
                <thead class="table-light">
                  <tr>
                    <th>Código</th>
                    <th>Nome</th>
                    <th>Categoria</th>
                    <th>Tipo</th>
                    <th>Valor Base</th>
                    <th>Status</th>
                    <th width="120">Ações</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="procedimento in procedimentos" :key="procedimento.id">
                    <td>
                      <code v-if="procedimento.codigo" class="text-primary">{{ procedimento.codigo }}</code>
                      <span v-else class="text-muted">-</span>
                    </td>
                    <td>
                      <div>
                        <strong>{{ procedimento.nome }}</strong>
                        <small v-if="procedimento.descricao" class="d-block text-muted">
                          {{ procedimento.descricao.substring(0, 60) }}{{ procedimento.descricao.length > 60 ? '...' : '' }}
                        </small>
                      </div>
                    </td>
                    <td>
                      <span 
                        v-if="procedimento.categoria" 
                        class="badge rounded-pill"
                        :style="{ backgroundColor: procedimento.categoria.cor, color: '#fff' }"
                      >
                        {{ procedimento.categoria.nome }}
                      </span>
                      <span v-else class="text-muted">-</span>
                    </td>
                    <td>
                      <span class="badge" :class="getTipoBadgeClass(procedimento.tipo)">
                        {{ getTipoLabel(procedimento.tipo) }}
                      </span>
                    </td>
                    <td>
                      <strong class="text-success">{{ formatarMoeda(procedimento.valor_base) }}</strong>
                      <small v-if="procedimento.valor_minimo || procedimento.valor_maximo" class="d-block text-muted">
                        {{ getFaixaValor(procedimento) }}
                      </small>
                    </td>
                    <td>
                      <div class="form-check form-switch">
                        <input 
                          class="form-check-input" 
                          type="checkbox" 
                          :checked="procedimento.ativo"
                          @change="toggleStatus(procedimento)"
                          :disabled="procedimento.toggleLoading"
                        >
                        <label class="form-check-label">
                          {{ procedimento.ativo ? 'Ativo' : 'Inativo' }}
                        </label>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group btn-group-sm">
                        <button 
                          class="btn btn-outline-primary"
                          @click="abrirModalProcedimento(procedimento)"
                          title="Editar"
                        >
                          <i class="fas fa-edit"></i>
                        </button>
                        <button 
                          class="btn btn-outline-danger"
                          @click="excluirProcedimento(procedimento)"
                          title="Excluir"
                        >
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Paginação -->
            <div v-if="paginacao && paginacao.last_page > 1" class="d-flex justify-content-between align-items-center p-3 border-top">
              <div class="text-muted">
                Mostrando {{ paginacao.from }} a {{ paginacao.to }} de {{ paginacao.total }} registros
              </div>
              <nav>
                <ul class="pagination pagination-sm mb-0">
                  <li class="page-item" :class="{ disabled: paginacao.current_page === 1 }">
                    <a class="page-link" href="#" @click.prevent="irParaPagina(paginacao.current_page - 1)">
                      <i class="fas fa-chevron-left"></i>
                    </a>
                  </li>
                  <li 
                    v-for="page in getVisiblePages()" 
                    :key="page" 
                    class="page-item" 
                    :class="{ active: page === paginacao.current_page }"
                  >
                    <a class="page-link" href="#" @click.prevent="irParaPagina(page)">{{ page }}</a>
                  </li>
                  <li class="page-item" :class="{ disabled: paginacao.current_page === paginacao.last_page }">
                    <a class="page-link" href="#" @click.prevent="irParaPagina(paginacao.current_page + 1)">
                      <i class="fas fa-chevron-right"></i>
                    </a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <ModalProcedimento 
      ref="modalProcedimento"
      @procedimento-salvo="onProcedimentoSalvo"
    />
    
    <ModalCategoria 
      ref="modalCategoria"
      @categoria-salva="onCategoriaSalva"
    />
  </div>
</template>

<script>
import { servicoProdutoService } from '@/services/servicoProdutoService';
import categoriaServicoService from '@/services/categoriaServicoService';
import ModalProcedimento from '@/components/ModalProcedimento.vue';
import ModalCategoria from '@/components/ModalCategoria.vue';
import cSwal from '@/utils/cSwal';

export default {
  name: 'TabelaProcedimentos',
  components: {
    ModalProcedimento,
    ModalCategoria
  },
  data() {
    return {
      procedimentos: [],
      categorias: [],
      loading: false,
      paginacao: null,
      filtros: {
        busca: '',
        categoria_id: '',
        tipo: '',
        ativo: '',
        page: 1
      },
      debounceTimer: null
    };
  },
  async created() {
    await this.carregarDados();
  },
  methods: {
    async carregarDados() {
      await Promise.all([
        this.carregarProcedimentos(),
        this.carregarCategorias()
      ]);
    },

    async carregarProcedimentos() {
      this.loading = true;
      try {
        const response = await servicoProdutoService.getServicosProdutos(this.filtros);
        if (response.data.success) {
          this.procedimentos = response.data.data.data;
          this.paginacao = {
            current_page: response.data.data.current_page,
            last_page: response.data.data.last_page,
            from: response.data.data.from,
            to: response.data.data.to,
            total: response.data.data.total
          };
        }
      } catch (error) {
        console.error('Erro ao carregar procedimentos:', error);
        cSwal.cError('Erro ao carregar procedimentos');
      } finally {
        this.loading = false;
      }
    },

    async carregarCategorias() {
      try {
        const response = await categoriaServicoService.getCategorias();
        if (response.data.success) {
          this.categorias = response.data.data;
        }
      } catch (error) {
        console.error('Erro ao carregar categorias:', error);
      }
    },

    buscarProcedimentos() {
      // Debounce para evitar muitas requisições
      clearTimeout(this.debounceTimer);
      this.debounceTimer = setTimeout(() => {
        this.filtros.page = 1;
        this.carregarProcedimentos();
      }, 300);
    },

    limparFiltros() {
      this.filtros = {
        busca: '',
        categoria_id: '',
        tipo: '',
        ativo: '',
        page: 1
      };
      this.carregarProcedimentos();
    },

    irParaPagina(page) {
      if (page >= 1 && page <= this.paginacao.last_page) {
        this.filtros.page = page;
        this.carregarProcedimentos();
      }
    },

    getVisiblePages() {
      const current = this.paginacao.current_page;
      const last = this.paginacao.last_page;
      const pages = [];
      
      const start = Math.max(1, current - 2);
      const end = Math.min(last, current + 2);
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      
      return pages;
    },

    abrirModalProcedimento(procedimento = null) {
      this.$refs.modalProcedimento.abrirModal(procedimento);
    },

    abrirModalCategoria(categoria = null) {
      this.$refs.modalCategoria.abrirModal(categoria);
    },

    async toggleStatus(procedimento) {
      procedimento.toggleLoading = true;
      try {
        const response = await servicoProdutoService.toggleStatus(procedimento.id);
        if (response.data.success) {
          procedimento.ativo = !procedimento.ativo;
          cSwal.cSuccess(response.data.message);
        }
      } catch (error) {
        console.error('Erro ao alterar status:', error);
        cSwal.cError('Erro ao alterar status do procedimento');
      } finally {
        procedimento.toggleLoading = false;
      }
    },

    async excluirProcedimento(procedimento) {
      const result = await cSwal.cConfirm(
        `Tem certeza que deseja excluir o procedimento "${procedimento.nome}"?`,
        'Esta ação não pode ser desfeita!'
      );

      if (result.isConfirmed) {
        try {
          const response = await servicoProdutoService.excluirServicoProduto(procedimento.id);
          if (response.data.success) {
            cSwal.cSuccess('Procedimento excluído com sucesso!');
            this.carregarProcedimentos();
          }
        } catch (error) {
          console.error('Erro ao excluir procedimento:', error);
          cSwal.cError('Erro ao excluir procedimento: ' + (error.response?.data?.message || error.message));
        }
      }
    },

    onProcedimentoSalvo() {
      this.carregarProcedimentos();
    },

    onCategoriaSalva() {
      this.carregarCategorias();
    },

    getTipoLabel(tipo) {
      const tipos = {
        'servico': 'Serviço',
        'produto': 'Produto',
        'procedimento': 'Procedimento'
      };
      return tipos[tipo] || tipo;
    },

    getTipoBadgeClass(tipo) {
      const classes = {
        'servico': 'bg-primary',
        'produto': 'bg-warning',
        'procedimento': 'bg-success'
      };
      return classes[tipo] || 'bg-secondary';
    },

    formatarMoeda(valor) {
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(valor);
    },

    getFaixaValor(procedimento) {
      if (procedimento.valor_minimo && procedimento.valor_maximo) {
        return `${this.formatarMoeda(procedimento.valor_minimo)} - ${this.formatarMoeda(procedimento.valor_maximo)}`;
      }
      return '';
    }
  }
};
</script>

<style scoped>
.bg-gradient-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.15s ease-in-out;
}

.table th {
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
}

.table td {
  vertical-align: middle;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
}

.form-switch .form-check-input {
  cursor: pointer;
}

.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
}

.badge {
  font-size: 0.75em;
}

code {
  font-size: 0.875em;
}
</style>
