import axios from '@/services/axios';

export const servicoProdutoService = {
  /**
   * Obter todos os serviços/produtos com filtros
   */
  async getServicosProdutos(filters = {}) {
    const params = new URLSearchParams();
    
    if (filters.categoria_id) params.append('categoria_id', filters.categoria_id);
    if (filters.tipo) params.append('tipo', filters.tipo);
    if (filters.ativo !== undefined) params.append('ativo', filters.ativo);
    if (filters.busca) params.append('busca', filters.busca);
    if (filters.page) params.append('page', filters.page);

    return axios.get(`/servicos-produtos?${params.toString()}`);
  },

  /**
   * Obter serviço/produto específico
   */
  async getServicoProduto(id) {
    return axios.get(`/servicos-produtos/${id}`);
  },

  /**
   * Criar novo serviço/produto
   */
  async criarServicoProduto(data) {
    return axios.post('/servicos-produtos', data);
  },

  /**
   * Atualizar serviço/produto
   */
  async atualizarServicoProduto(id, data) {
    return axios.put(`/servicos-produtos/${id}`, data);
  },

  /**
   * Excluir serviço/produto
   */
  async excluirServicoProduto(id) {
    return axios.delete(`/servicos-produtos/${id}`);
  },

  /**
   * Ativar/Desativar serviço/produto
   */
  async toggleStatus(id) {
    return axios.post(`/servicos-produtos/${id}/toggle-status`);
  },

  /**
   * Obter tipos disponíveis
   */
  async getTipos() {
    return axios.get('/servicos-produtos/tipos');
  },

  /**
   * Buscar serviços/produtos para orçamento
   */
  async buscarParaOrcamento(filters = {}) {
    const params = new URLSearchParams();

    if (filters.busca) params.append('busca', filters.busca);
    if (filters.tipo) params.append('tipo', filters.tipo);

    return axios.get(`/servicos-produtos/buscar-para-orcamento?${params.toString()}`);
  },

  /**
   * Obter categorias de serviços
   */
  async getCategorias(filters = {}) {
    const params = new URLSearchParams();

    if (filters.ativo !== undefined) params.append('ativo', filters.ativo);

    return axios.get(`/categorias-servicos?${params.toString()}`);
  },

  /**
   * Obter categorias ativas para seleção
   */
  async getCategoriasAtivas() {
    return axios.get('/categorias-servicos/ativas');
  },

  /**
   * Criar nova categoria
   */
  async createCategoria(data) {
    return axios.post('/categorias-servicos', data);
  },

  /**
   * Atualizar categoria
   */
  async updateCategoria(id, data) {
    return axios.put(`/categorias-servicos/${id}`, data);
  },

  /**
   * Excluir categoria
   */
  async deleteCategoria(id) {
    return axios.delete(`/categorias-servicos/${id}`);
  },

  /**
   * Ativar/Desativar categoria
   */
  async toggleCategoriaStatus(id) {
    return axios.post(`/categorias-servicos/${id}/toggle-status`);
  },

  /**
   * Validar dados de serviço/produto
   */
  validateServicoProdutoData(data) {
    const errors = {};

    if (!data.nome || data.nome.trim() === '') {
      errors.nome = 'Nome é obrigatório';
    }

    if (!data.tipo) {
      errors.tipo = 'Tipo é obrigatório';
    }

    if (!data.valor_base || data.valor_base <= 0) {
      errors.valor_base = 'Valor base deve ser maior que zero';
    }

    if (data.valor_minimo && data.valor_minimo > data.valor_base) {
      errors.valor_minimo = 'Valor mínimo não pode ser maior que o valor base';
    }

    if (data.valor_maximo && data.valor_maximo < data.valor_base) {
      errors.valor_maximo = 'Valor máximo não pode ser menor que o valor base';
    }

    if (data.tempo_estimado && data.tempo_estimado < 1) {
      errors.tempo_estimado = 'Tempo estimado deve ser maior que zero';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },

  /**
   * Validar dados de categoria
   */
  validateCategoriaData(data) {
    const errors = {};

    if (!data.nome || data.nome.trim() === '') {
      errors.nome = 'Nome é obrigatório';
    }

    if (data.cor && !/^#[0-9A-Fa-f]{6}$/.test(data.cor)) {
      errors.cor = 'Cor deve estar no formato hexadecimal (#RRGGBB)';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },

  /**
   * Formatar valor monetário
   */
  formatCurrency(value) {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0);
  },

  /**
   * Formatar tempo estimado
   */
  formatTempo(minutos) {
    if (!minutos) return 'Não definido';

    const horas = Math.floor(minutos / 60);
    const mins = minutos % 60;

    if (horas > 0) {
      return mins > 0 ? `${horas}h ${mins}min` : `${horas}h`;
    }

    return `${mins}min`;
  },

  /**
   * Obter texto do tipo
   */
  getTipoText(tipo) {
    const tipos = {
      'servico': 'Serviço',
      'produto': 'Produto',
      'procedimento': 'Procedimento'
    };
    
    return tipos[tipo] || tipo;
  },

  /**
   * Obter classe CSS do tipo
   */
  getTipoClass(tipo) {
    const classes = {
      'servico': 'badge-info',
      'produto': 'badge-warning',
      'procedimento': 'badge-success'
    };
    
    return classes[tipo] || 'badge-secondary';
  },

  /**
   * Obter ícone do tipo
   */
  getTipoIcon(tipo) {
    const icons = {
      'servico': 'fas fa-handshake',
      'produto': 'fas fa-box',
      'procedimento': 'fas fa-user-md'
    };
    
    return icons[tipo] || 'fas fa-tag';
  },

  /**
   * Criar serviço/produto vazio
   */
  createEmptyServicoProduto() {
    return {
      categoria_id: null,
      codigo: '',
      nome: '',
      descricao: '',
      tipo: 'servico',
      valor_base: 0,
      valor_minimo: null,
      valor_maximo: null,
      unidade: 'un',
      tempo_estimado: null,
      observacoes: ''
    };
  },

  /**
   * Criar categoria vazia
   */
  createEmptyCategoria() {
    return {
      nome: '',
      descricao: '',
      cor: '#007bff'
    };
  },

  /**
   * Validar valor dentro da faixa permitida
   */
  validateValorFaixa(valor, valorMinimo, valorMaximo) {
    if (valorMinimo && valor < valorMinimo) {
      return {
        valid: false,
        message: `Valor deve ser maior ou igual a ${this.formatCurrency(valorMinimo)}`
      };
    }

    if (valorMaximo && valor > valorMaximo) {
      return {
        valid: false,
        message: `Valor deve ser menor ou igual a ${this.formatCurrency(valorMaximo)}`
      };
    }

    return { valid: true };
  },

  /**
   * Obter faixa de valor formatada
   */
  getValorFaixaFormatada(valorBase, valorMinimo, valorMaximo) {
    if (valorMinimo && valorMaximo) {
      return `${this.formatCurrency(valorMinimo)} - ${this.formatCurrency(valorMaximo)}`;
    }

    if (valorMinimo) {
      return `A partir de ${this.formatCurrency(valorMinimo)}`;
    }

    if (valorMaximo) {
      return `Até ${this.formatCurrency(valorMaximo)}`;
    }

    return this.formatCurrency(valorBase);
  },

  /**
   * Gerar código automático
   */
  generateCodigo(tipo, sequencial) {
    const prefixos = {
      'servico': 'SERV',
      'produto': 'PROD',
      'procedimento': 'PROC'
    };

    const prefixo = prefixos[tipo] || 'ITEM';
    return `${prefixo}${String(sequencial).padStart(3, '0')}`;
  },

  /**
   * Cores padrão para categorias
   */
  getCoresPadrao() {
    return [
      '#007bff', // Azul
      '#28a745', // Verde
      '#ffc107', // Amarelo
      '#dc3545', // Vermelho
      '#6f42c1', // Roxo
      '#fd7e14', // Laranja
      '#20c997', // Teal
      '#e83e8c', // Rosa
      '#6c757d', // Cinza
      '#17a2b8'  // Ciano
    ];
  }
};

export default servicoProdutoService;
